import { PrismaClient } from "../../prisma/src/generated/prisma";

import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";

dayjs.extend(utc);
dayjs.extend(timezone);

// 默认时区（中国北京时间）
const DEFAULT_TZ = "Asia/Shanghai";



const globalForPrisma = global as unknown as {
  prisma: PrismaClient | undefined;
};

 const prisma = globalForPrisma.prisma ?? new PrismaClient();
// 自动时区中间件：查询返回的所有 Date 字段自动转北京时间
prisma.$use(async (params, next) => {
  const result = await next(params);

  const convertDate = (obj: any): any => {
    if (!obj) return obj;

    if (obj instanceof Date) {
      return dayjs(obj).tz(DEFAULT_TZ).toDate();
    }

    if (Array.isArray(obj)) {
      return obj.map(convertDate);
    }

    if (typeof obj === "object") {
      return Object.fromEntries(Object.entries(obj).map(([k, v]) => [k, convertDate(v)]));
    }

    return obj;
  };

  return convertDate(result);
});


if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;

e