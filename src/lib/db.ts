import { PrismaClient } from "../../prisma/src/generated/prisma";

import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";

dayjs.extend(utc);
dayjs.extend(timezone);

// 默认时区（中国北京时间）
const DEFAULT_TZ = "Asia/Shanghai";



// 时区转换函数
const convertDate = (obj: any): any => {
  if (!obj) return obj;

  if (obj instanceof Date) {
    return dayjs(obj).tz(DEFAULT_TZ).toDate();
  }

  if (Array.isArray(obj)) {
    return obj.map(convertDate);
  }

  if (typeof obj === "object") {
    return Object.fromEntries(Object.entries(obj).map(([k, v]) => [k, convertDate(v)]));
  }

  return obj;
};

// 使用 Prisma Client Extensions 替代已废弃的 $use 中间件
const prisma = new PrismaClient().$extends({
  result: {
    // 为所有模型添加时区转换
    $allModels: {
      // 这里可以添加计算字段，但对于时区转换，我们需要使用查询扩展
    }
  },
  query: {
    // 为所有模型的所有查询操作添加时区转换
    $allModels: {
      async $allOperations({ operation: _operation, model: _model, args, query }) {
        const result = await query(args);
        return convertDate(result);
      }
    }
  }
});


export default prisma;
