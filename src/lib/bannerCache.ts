import { prisma } from "@/lib/db";
import redis from "@/lib/redis";

export const refreshBannerCache = async (
  cacheKey: string,
  position: string
) => {
  const banners = await prisma.banner.findMany({
    where: {
      status: true,

      position,
    },
    orderBy: { sort: "desc" },
  });

  await redis.set(cacheKey, JSON.stringify(banners), "EX", 60 * 5);
  console.log(`🔄 Redis cacheKey: ${cacheKey} 缓存已刷新`);
};

export const bannnerCacheKey = (position: string) => {
  return `${position}banners`;
};
