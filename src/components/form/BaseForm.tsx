"use client";

import React from "react";
import { Form, Button, Space, Card, Divider } from "antd";
import type { FormInstance, FormProps } from "antd/es/form";
import { useResponsive, useResponsiveConfig } from "@/hooks/useResponsive";

// 表单配置接口
export interface BaseFormProps extends Omit<FormProps, "onFinish"> {
  // 基础配置
  title?: string;
  description?: string;

  // 表单实例
  form?: FormInstance;

  // 提交相关
  onFinish?: (values: any) => Promise<void> | void;
  onCancel?: () => void;

  // 按钮配置
  submitText?: string;
  cancelText?: string;
  showCancel?: boolean;
  submitButtonProps?: any;
  cancelButtonProps?: any;

  // 状态
  loading?: boolean;
  disabled?: boolean;

  // 布局配置
  layout?: "horizontal" | "vertical" | "inline";
  labelCol?: any;
  wrapperCol?: any;

  // 样式配置
  bordered?: boolean;
  size?: "small" | "middle" | "large";
  className?: string;
  style?: React.CSSProperties;

  // 子组件
  children?: React.ReactNode;

  // 额外操作
  extra?: React.ReactNode;

  // 分组配置
  sections?: Array<{
    title: string;
    description?: string;
    children: React.ReactNode;
  }>;
}

// 基础表单组件
export default function BaseForm({
  title,
  description,
  form,
  onFinish,
  onCancel,
  submitText = "提交",
  cancelText = "取消",
  showCancel = true,
  submitButtonProps = {},
  cancelButtonProps = {},
  loading = false,
  disabled = false,
  layout = "vertical",
  labelCol,
  wrapperCol,
  bordered = true,
  size = "middle",
  className,
  style,
  children,
  extra,
  sections,
  ...formProps
}: BaseFormProps) {
  const { isMobile, isTablet, isSmallScreen } = useResponsive();
  const { form: formConfig, button: buttonConfig } = useResponsiveConfig();

  // 处理表单提交
  const handleFinish = async (values: any) => {
    try {
      await onFinish?.(values);
    } catch (error) {
      console.error("表单提交错误:", error);
    }
  };

  // 渲染表单内容
  const renderFormContent = () => {
    if (sections && sections.length > 0) {
      return sections.map((section, index) => (
        <div key={index}>
          {index > 0 && <Divider />}
          <div style={{ marginBottom: "16px" }}>
            <h3 style={{ margin: "0 0 4px 0" }}>{section.title}</h3>
            {section.description && (
              <p style={{ margin: 0, color: "#666", fontSize: "14px" }}>
                {section.description}
              </p>
            )}
          </div>
          {section.children}
        </div>
      ));
    }
    return children;
  };

  // 渲染操作按钮
  const renderActions = () => (
    <div
      style={{
        display: "flex",
        justifyContent: isSmallScreen ? "stretch" : "flex-end",
        flexDirection: isSmallScreen ? "column-reverse" : "row",
        gap: isMobile ? "8px" : "12px",
        marginTop: isMobile ? "16px" : "24px",
        paddingTop: isMobile ? "12px" : "16px",
        borderTop: "1px solid #f0f0f0",
      }}
    >
      {extra}
      <Space
        direction={isSmallScreen ? "vertical" : "horizontal"}
        style={{ width: isSmallScreen ? "100%" : "auto" }}
      >
        {showCancel && onCancel && (
          <Button
            onClick={onCancel}
            size={buttonConfig.size}
            block={buttonConfig.block}
            disabled={loading}
            {...cancelButtonProps}
          >
            {cancelText}
          </Button>
        )}
        <Button
          type="primary"
          htmlType="submit"
          loading={loading}
          disabled={disabled}
          {...submitButtonProps}
        >
          {submitText}
        </Button>
      </Space>
    </div>
  );

  const formContent = (
    <Form
      form={form}
      layout={formConfig.layout as any}
      labelCol={formConfig.labelCol || labelCol}
      wrapperCol={formConfig.wrapperCol || wrapperCol}
      size={formConfig.size as any}
      disabled={disabled}
      onFinish={handleFinish}
      {...formProps}
    >
      {renderFormContent()}
      {renderActions()}
    </Form>
  );

  // 如果有标题或描述，使用Card包装
  if (title || description) {
    return (
      <Card
        title={title}
        bordered={bordered}
        className={className}
        style={style}
      >
        {description && (
          <p style={{ margin: "0 0 24px 0", color: "#666" }}>{description}</p>
        )}
        {formContent}
      </Card>
    );
  }

  return (
    <div className={className} style={style}>
      {formContent}
    </div>
  );
}

// 表单字段包装器
export interface FormFieldProps {
  label: string;
  name: string;
  required?: boolean;
  rules?: any[];
  tooltip?: string;
  help?: string;
  extra?: string;
  children: React.ReactNode;
}

export function FormField({
  label,
  name,
  required = false,
  rules = [],
  tooltip,
  help,
  extra,
  children,
}: FormFieldProps) {
  const fieldRules = [
    ...(required ? [{ required: true, message: `请输入${label}` }] : []),
    ...rules,
  ];

  return (
    <Form.Item
      label={label}
      name={name}
      rules={fieldRules}
      tooltip={tooltip}
      help={help}
      extra={extra}
    >
      {children}
    </Form.Item>
  );
}

// 表单工具函数
export const formUtils = {
  // 创建验证规则
  createRules: {
    required: (message?: string) => ({ required: true, message }),
    email: (message = "请输入有效的邮箱地址") => ({
      type: "email" as const,
      message,
    }),
    phone: (message = "请输入有效的手机号") => ({
      pattern: /^1[3-9]\d{9}$/,
      message,
    }),
    url: (message = "请输入有效的URL") => ({ type: "url" as const, message }),
    min: (min: number, message?: string) => ({
      min,
      message: message || `最少${min}个字符`,
    }),
    max: (max: number, message?: string) => ({
      max,
      message: message || `最多${max}个字符`,
    }),
    pattern: (pattern: RegExp, message: string) => ({ pattern, message }),
    custom: (validator: (rule: any, value: any) => Promise<void>) => ({
      validator,
    }),
  },

  // 表单布局配置
  layouts: {
    horizontal: {
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
    },
    vertical: {
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
    },
    inline: {
      layout: "inline" as const,
    },
  },

  // 常用初始值
  getInitialValues: (data: any, fields: string[]) => {
    const initialValues: any = {};
    fields.forEach((field) => {
      if (data && data[field] !== undefined) {
        initialValues[field] = data[field];
      }
    });
    return initialValues;
  },

  // 表单验证
  validateFields: async (form: FormInstance, fields?: string[]) => {
    try {
      const values = await form.validateFields(fields);
      return { success: true, values };
    } catch (error) {
      return { success: false, error };
    }
  },

  // 重置表单
  resetForm: (form: FormInstance, initialValues?: any) => {
    form.resetFields();
    if (initialValues) {
      form.setFieldsValue(initialValues);
    }
  },
};
