"use client";

import React from "react";
import {
  Input,
  Select,
  DatePicker,
  TimePicker,
  InputNumber,
  Switch,
  Radio,
  Checkbox,
  Upload,
  TreeSelect,
  Cascader,
  Rate,
  Slider,
  ColorPicker,
  AutoComplete,
} from "antd";
import { UploadOutlined, InboxOutlined } from "@ant-design/icons";
import { FormField, formUtils } from "./BaseForm";
import type { FormFieldProps } from "./BaseForm";

const { TextArea, Password } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { Group: RadioGroup } = Radio;
const { Group: CheckboxGroup } = Checkbox;
const { Dragger } = Upload;

// 基础字段属性接口
interface BaseFieldProps extends Omit<FormFieldProps, "children"> {
  placeholder?: string;
  disabled?: boolean;
  size?: "small" | "middle" | "large";
}

// 文本输入框
export interface TextFieldProps extends BaseFieldProps {
  maxLength?: number;
  showCount?: boolean;
  prefix?: React.ReactNode;
  suffix?: React.ReactNode;
  addonBefore?: React.ReactNode;
  addonAfter?: React.ReactNode;
}

export function TextField({
  maxLength,
  showCount,
  prefix,
  suffix,
  addonBefore,
  addonAfter,
  placeholder,
  disabled,
  size,
  ...fieldProps
}: TextFieldProps) {
  return (
    <FormField {...fieldProps}>
      <Input
        placeholder={placeholder}
        disabled={disabled}
        size={size}
        maxLength={maxLength}
        showCount={showCount}
        prefix={prefix}
        suffix={suffix}
        addonBefore={addonBefore}
        addonAfter={addonAfter}
      />
    </FormField>
  );
}

// 密码输入框
export interface PasswordFieldProps extends BaseFieldProps {
  visibilityToggle?: boolean;
}

export function PasswordField({
  visibilityToggle = true,
  placeholder,
  disabled,
  size,
  ...fieldProps
}: PasswordFieldProps) {
  return (
    <FormField {...fieldProps}>
      <Password
        placeholder={placeholder}
        disabled={disabled}
        size={size}
        visibilityToggle={visibilityToggle}
      />
    </FormField>
  );
}

// 文本域
export interface TextAreaFieldProps extends BaseFieldProps {
  rows?: number;
  maxLength?: number;
  showCount?: boolean;
  autoSize?: boolean | { minRows?: number; maxRows?: number };
}

export function TextAreaField({
  rows = 4,
  maxLength,
  showCount,
  autoSize,
  placeholder,
  disabled,
  ...fieldProps
}: TextAreaFieldProps) {
  return (
    <FormField {...fieldProps}>
      <TextArea
        placeholder={placeholder}
        disabled={disabled}
        rows={rows}
        maxLength={maxLength}
        showCount={showCount}
        autoSize={autoSize}
      />
    </FormField>
  );
}

// 数字输入框
export interface NumberFieldProps extends BaseFieldProps {
  min?: number;
  max?: number;
  step?: number;
  precision?: number;
  formatter?: (value: number | string | undefined) => string;
  parser?: (displayValue: string | undefined) => number | string;
}

export function NumberField({
  min,
  max,
  step,
  precision,
  formatter,
  parser,
  placeholder,
  disabled,
  size,
  ...fieldProps
}: NumberFieldProps) {
  return (
    <FormField {...fieldProps}>
      <InputNumber
        placeholder={placeholder}
        disabled={disabled}
        size={size}
        min={min}
        max={max}
        step={step}
        precision={precision}
        formatter={formatter}
        parser={parser}
        style={{ width: "100%" }}
      />
    </FormField>
  );
}

// 选择框
export interface SelectFieldProps extends BaseFieldProps {
  options: Array<{ label: string; value: any; disabled?: boolean }>;
  mode?: "multiple" | "tags";
  allowClear?: boolean;
  showSearch?: boolean;
  filterOption?: boolean | ((input: string, option: any) => boolean);
  loading?: boolean;
}

export function SelectField({
  options,
  mode,
  allowClear = true,
  showSearch = false,
  filterOption,
  loading,
  placeholder,
  disabled,
  size,
  ...fieldProps
}: SelectFieldProps) {
  return (
    <FormField {...fieldProps}>
      <Select
        placeholder={placeholder}
        disabled={disabled}
        size={size}
        mode={mode}
        allowClear={allowClear}
        showSearch={showSearch}
        filterOption={filterOption}
        loading={loading}
        options={options}
      />
    </FormField>
  );
}

// 日期选择器
export interface DateFieldProps extends BaseFieldProps {
  picker?: "date" | "week" | "month" | "quarter" | "year";
  format?: string;
  showTime?: boolean;
  disabledDate?: (currentDate: any) => boolean;
}

export function DateField({
  picker = "date",
  format,
  showTime,
  disabledDate,
  placeholder,
  disabled,
  size,
  ...fieldProps
}: DateFieldProps) {
  return (
    <FormField {...fieldProps}>
      <DatePicker
        placeholder={placeholder}
        disabled={disabled}
        size={size}
        picker={picker}
        format={format}
        showTime={showTime}
        disabledDate={disabledDate}
        style={{ width: "100%" }}
      />
    </FormField>
  );
}

// 日期范围选择器
export interface DateRangeFieldProps extends BaseFieldProps {
  picker?: "date" | "week" | "month" | "quarter" | "year";
  format?: string;
  showTime?: boolean;
  disabledDate?: (currentDate: any) => boolean;
}

export function DateRangeField({
  picker = "date",
  format,
  showTime,
  disabledDate,
  placeholder,
  disabled,
  size,
  ...fieldProps
}: DateRangeFieldProps) {
  return (
    <FormField {...fieldProps}>
      <RangePicker
        placeholder={placeholder as any}
        disabled={disabled}
        size={size}
        picker={picker}
        format={format}
        showTime={showTime}
        disabledDate={disabledDate}
        style={{ width: "100%" }}
      />
    </FormField>
  );
}

// 开关
export interface SwitchFieldProps extends BaseFieldProps {
  checkedChildren?: React.ReactNode;
  unCheckedChildren?: React.ReactNode;
}

export function SwitchField({
  checkedChildren,
  unCheckedChildren,
  disabled,
  size,
  ...fieldProps
}: SwitchFieldProps) {
  return (
    <FormField {...fieldProps}>
      <Switch
        disabled={disabled}
        size={size as any}
        checkedChildren={checkedChildren}
        unCheckedChildren={unCheckedChildren}
      />
    </FormField>
  );
}

// 单选框组
export interface RadioFieldProps extends BaseFieldProps {
  options: Array<{ label: string; value: any; disabled?: boolean }>;
  optionType?: "default" | "button";
  buttonStyle?: "outline" | "solid";
}

export function RadioField({
  options,
  optionType = "default",
  buttonStyle = "outline",
  disabled,
  size,
  ...fieldProps
}: RadioFieldProps) {
  return (
    <FormField {...fieldProps}>
      <RadioGroup
        disabled={disabled}
        size={size}
        optionType={optionType}
        buttonStyle={buttonStyle}
        options={options}
      />
    </FormField>
  );
}

// 复选框组
export interface CheckboxFieldProps extends BaseFieldProps {
  options: Array<{ label: string; value: any; disabled?: boolean }>;
}

export function CheckboxField({
  options,
  disabled,
  ...fieldProps
}: CheckboxFieldProps) {
  return (
    <FormField {...fieldProps}>
      <CheckboxGroup disabled={disabled} options={options} />
    </FormField>
  );
}

// 文件上传
export interface UploadFieldProps extends BaseFieldProps {
  action?: string;
  accept?: string;
  multiple?: boolean;
  maxCount?: number;
  listType?: "text" | "picture" | "picture-card";
  dragger?: boolean;
  beforeUpload?: (file: any) => boolean | Promise<any>;
  onChange?: (info: any) => void;
}

export function UploadField({
  action,
  accept,
  multiple,
  maxCount,
  listType = "text",
  dragger = false,
  beforeUpload,
  onChange,
  disabled,
  ...fieldProps
}: UploadFieldProps) {
  const uploadProps = {
    action,
    accept,
    multiple,
    maxCount,
    listType,
    disabled,
    beforeUpload,
    onChange,
  };

  return (
    <FormField {...fieldProps}>
      {dragger ? (
        <Dragger {...uploadProps}>
          <p className="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p className="ant-upload-hint">支持单个或批量上传</p>
        </Dragger>
      ) : (
        <Upload {...uploadProps}>
          <button type="button" disabled={disabled}>
            <UploadOutlined /> 选择文件
          </button>
        </Upload>
      )}
    </FormField>
  );
}

// 评分
export interface RateFieldProps extends BaseFieldProps {
  count?: number;
  allowHalf?: boolean;
  allowClear?: boolean;
  character?: React.ReactNode;
  tooltips?: string[];
}

export function RateField({
  count = 5,
  allowHalf = false,
  allowClear = true,
  character,
  tooltips,
  disabled,
  ...fieldProps
}: RateFieldProps) {
  return (
    <FormField {...fieldProps}>
      <Rate
        count={count}
        allowHalf={allowHalf}
        allowClear={allowClear}
        character={character}
        tooltips={tooltips}
        disabled={disabled}
      />
    </FormField>
  );
}

// 滑动输入条
export interface SliderFieldProps extends BaseFieldProps {
  min?: number;
  max?: number;
  step?: number;
  marks?: Record<number, React.ReactNode>;
  range?: boolean;
  vertical?: boolean;
}

export function SliderField({
  min = 0,
  max = 100,
  step = 1,
  marks,
  range,
  vertical,
  disabled,
  ...fieldProps
}: SliderFieldProps) {
  return (
    <FormField {...fieldProps}>
      <Slider
        min={min}
        max={max}
        step={step}
        marks={marks}
        range={range}
        vertical={vertical}
        disabled={disabled}
      />
    </FormField>
  );
}

// 导出所有字段组件
export { formUtils } from "./BaseForm";
