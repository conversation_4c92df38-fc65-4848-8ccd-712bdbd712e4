// 导出基础表单组件
export { default as BaseForm } from './BaseForm';
export type { BaseFormProps, FormFieldProps } from './BaseForm';
export { FormField, formUtils } from './BaseForm';

// 导出所有表单字段组件
export {
  TextField,
  PasswordField,
  TextAreaField,
  NumberField,
  SelectField,
  DateField,
  DateRangeField,
  SwitchField,
  RadioField,
  CheckboxField,
  UploadField,
  RateField,
  SliderField,
} from './FormFields';

// 导出字段组件的类型
export type {
  TextFieldProps,
  PasswordFieldProps,
  TextAreaFieldProps,
  NumberFieldProps,
  SelectFieldProps,
  DateFieldProps,
  DateRangeFieldProps,
  SwitchFieldProps,
  RadioFieldProps,
  CheckboxFieldProps,
  UploadFieldProps,
  RateFieldProps,
  SliderFieldProps,
} from './FormFields';

// 导出表单模板
export { default as UserForm } from './templates/UserForm';
export { default as LoginForm } from './templates/LoginForm';
export { default as SettingsForm } from './templates/SettingsForm';

// 表单工具函数
export const formHelpers = {
  // 常用验证规则
  rules: {
    required: (message?: string) => ({ required: true, message }),
    email: { type: 'email' as const, message: '请输入有效的邮箱地址' },
    phone: { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号' },
    url: { type: 'url' as const, message: '请输入有效的URL' },
    password: { min: 6, message: '密码至少6位' },
    username: { 
      pattern: /^[a-zA-Z0-9_]{3,20}$/, 
      message: '用户名只能包含字母、数字和下划线，长度3-20位' 
    },
  },

  // 常用选项
  options: {
    gender: [
      { label: '男', value: 'male' },
      { label: '女', value: 'female' },
      { label: '其他', value: 'other' },
    ],
    status: [
      { label: '启用', value: 'active' },
      { label: '禁用', value: 'inactive' },
    ],
    priority: [
      { label: '低', value: 'low' },
      { label: '中', value: 'medium' },
      { label: '高', value: 'high' },
      { label: '紧急', value: 'urgent' },
    ],
    yesNo: [
      { label: '是', value: true },
      { label: '否', value: false },
    ],
  },

  // 表单布局预设
  layouts: {
    horizontal: {
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
    },
    vertical: {
      labelCol: { span: 24 },
      wrapperCol: { span: 24 },
    },
    inline: {
      layout: 'inline' as const,
    },
    modal: {
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
    },
    drawer: {
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
    },
  },

  // 表单尺寸
  sizes: {
    small: 'small' as const,
    middle: 'middle' as const,
    large: 'large' as const,
  },

  // 创建表单配置
  createFormConfig: (options: {
    layout?: 'horizontal' | 'vertical' | 'inline' | 'modal' | 'drawer';
    size?: 'small' | 'middle' | 'large';
    disabled?: boolean;
  } = {}) => {
    const { layout = 'vertical', size = 'middle', disabled = false } = options;
    
    return {
      layout: layout === 'inline' ? 'inline' : 'vertical',
      size,
      disabled,
      ...(layout !== 'inline' && layout !== 'vertical' ? formHelpers.layouts[layout] : {}),
    };
  },

  // 处理表单错误
  handleFormError: (error: any, form?: any) => {
    if (error?.errorFields) {
      // Ant Design 表单验证错误
      const firstError = error.errorFields[0];
      if (firstError && form) {
        form.scrollToField(firstError.name);
      }
      return firstError?.errors?.[0] || '表单验证失败';
    }
    
    if (error?.response?.data?.message) {
      // API 错误
      return error.response.data.message;
    }
    
    if (error?.message) {
      return error.message;
    }
    
    return '操作失败，请稍后重试';
  },

  // 格式化表单数据
  formatFormData: (values: any, options: {
    trimStrings?: boolean;
    removeEmpty?: boolean;
    dateFormat?: string;
  } = {}) => {
    const { trimStrings = true, removeEmpty = false, dateFormat = 'YYYY-MM-DD' } = options;
    const formatted = { ...values };
    
    Object.keys(formatted).forEach(key => {
      const value = formatted[key];
      
      // 处理字符串
      if (typeof value === 'string' && trimStrings) {
        formatted[key] = value.trim();
      }
      
      // 处理日期
      if (value && typeof value === 'object' && value.format) {
        formatted[key] = value.format(dateFormat);
      }
      
      // 处理日期范围
      if (Array.isArray(value) && value.length === 2 && value[0]?.format) {
        formatted[key] = value.map(date => date.format(dateFormat));
      }
      
      // 移除空值
      if (removeEmpty && (value === '' || value === null || value === undefined)) {
        delete formatted[key];
      }
    });
    
    return formatted;
  },

  // 设置表单初始值
  setInitialValues: (form: any, data: any, fields?: string[]) => {
    if (!form || !data) return;
    
    const values = fields ? 
      Object.keys(data)
        .filter(key => fields.includes(key))
        .reduce((obj, key) => ({ ...obj, [key]: data[key] }), {}) :
      data;
    
    form.setFieldsValue(values);
  },

  // 重置表单
  resetForm: (form: any, initialValues?: any) => {
    if (!form) return;
    
    form.resetFields();
    if (initialValues) {
      form.setFieldsValue(initialValues);
    }
  },

  // 验证表单
  validateForm: async (form: any, fields?: string[]) => {
    if (!form) return { success: false, error: '表单实例不存在' };
    
    try {
      const values = await form.validateFields(fields);
      return { success: true, values };
    } catch (error) {
      return { 
        success: false, 
        error: formHelpers.handleFormError(error, form) 
      };
    }
  },
};
