"use client";

import React from "react";
import { Form } from "antd";
import BaseForm from "../BaseForm";
import {
  TextField,
  PasswordField,
  SelectField,
  SwitchField,
  TextAreaField,
} from "../FormFields";
import { formHelpers } from "../index";
import type { BaseFormProps } from "../BaseForm";

// 用户表单数据接口
export interface UserFormData {
  username?: string;
  nickname: string;
  email: string;
  phone?: string;
  password?: string;
  confirmPassword?: string;
  roles: string[];
  status: "active" | "inactive" | "banned";
  avatar?: string;
  bio?: string;
  emailVerified?: boolean;
  phoneVerified?: boolean;
}

// 用户表单属性接口
export interface UserFormProps extends Omit<BaseFormProps, "children"> {
  // 表单模式
  mode?: "create" | "edit" | "profile";

  // 初始数据
  initialData?: Partial<UserFormData>;

  // 角色选项
  roleOptions?: Array<{ label: string; value: string }>;

  // 字段配置
  // fields?: [{
  //   username?: boolean;
  //   password?: boolean;
  //   confirmPassword?: boolean;
  //   roles?: boolean;
  //   status?: boolean;
  //   bio?: boolean;
  //   verification?: boolean;
  // }];
  fields?: any;

  // 提交处理
  onFinish?: (values: UserFormData) => Promise<void> | void;
}

// 默认角色选项
const defaultRoleOptions = [
  { label: "超级管理员", value: "super_admin" },
  { label: "管理员", value: "admin" },
  { label: "经理", value: "manager" },
  { label: "编辑员", value: "editor" },
  { label: "普通用户", value: "user" },
  { label: "访客", value: "guest" },
];

// 状态选项
const statusOptions = [
  { label: "正常", value: "active" },
  { label: "禁用", value: "inactive" },
  { label: "封禁", value: "banned" },
];

export default function UserForm({
  mode = "create",
  initialData,
  roleOptions = defaultRoleOptions,
  fields = {},
  onFinish,
  form,
  ...baseFormProps
}: UserFormProps) {
  const [formInstance] = Form.useForm(form);

  // 字段显示配置
  const showFields = {
    username: fields.username !== false && mode === "create",
    password: fields.password !== false && mode === "create",
    confirmPassword: fields.confirmPassword !== false && mode === "create",
    roles: fields.roles !== false && mode !== "profile",
    status: fields.status !== false && mode !== "profile",
    bio: fields.bio !== false,
    verification: fields.verification !== false && mode !== "create",
  };

  // 设置初始值
  React.useEffect(() => {
    if (initialData) {
      formHelpers.setInitialValues(formInstance, initialData);
    }
  }, [initialData, formInstance]);

  // 处理表单提交
  const handleFinish = async (values: UserFormData) => {
    try {
      // 格式化数据
      const formattedValues = formHelpers.formatFormData(values, {
        trimStrings: true,
        removeEmpty: true,
      });

      // 移除确认密码字段
      if (formattedValues.confirmPassword) {
        delete formattedValues.confirmPassword;
      }

      await onFinish?.(formattedValues);
    } catch (error) {
      console.error("用户表单提交错误:", error);
      throw error;
    }
  };

  // 密码确认验证
  const validateConfirmPassword = (_: any, value: string) => {
    if (!value) {
      return Promise.resolve();
    }

    const password = formInstance.getFieldValue("password");
    if (password && value !== password) {
      return Promise.reject(new Error("两次输入的密码不一致"));
    }

    return Promise.resolve();
  };

  return (
    <BaseForm
      {...baseFormProps}
      form={formInstance}
      onFinish={handleFinish}
      sections={[
        {
          title: "基本信息",
          description: "用户的基本身份信息",
          children: (
            <>
              {showFields.username && (
                <TextField
                  label="用户名"
                  name="username"
                  required
                  placeholder="请输入用户名"
                  rules={[
                    formHelpers.rules.required("请输入用户名"),
                    formHelpers.rules.username,
                  ]}
                  help="用户名只能包含字母、数字和下划线，长度3-20位"
                />
              )}

              <TextField
                label="昵称"
                name="nickname"
                required
                placeholder="请输入昵称"
                rules={[formHelpers.rules.required("请输入昵称")]}
              />

              <TextField
                label="邮箱"
                name="email"
                required
                placeholder="请输入邮箱地址"
                rules={[
                  formHelpers.rules.required("请输入邮箱"),
                  formHelpers.rules.email,
                ]}
              />

              <TextField
                label="手机号"
                name="phone"
                placeholder="请输入手机号"
                rules={[formHelpers.rules.phone]}
              />
            </>
          ),
        },

        ...(showFields.password
          ? [
              {
                title: "密码设置",
                description: "设置用户登录密码",
                children: (
                  <>
                    <PasswordField
                      label="密码"
                      name="password"
                      required
                      placeholder="请输入密码"
                      rules={[
                        formHelpers.rules.required("请输入密码"),
                        formHelpers.rules.password,
                      ]}
                      help="密码至少6位"
                    />

                    {showFields.confirmPassword && (
                      <PasswordField
                        label="确认密码"
                        name="confirmPassword"
                        required
                        placeholder="请再次输入密码"
                        rules={[
                          formHelpers.rules.required("请确认密码"),
                          { validator: validateConfirmPassword },
                        ]}
                      />
                    )}
                  </>
                ),
              },
            ]
          : []),

        ...(showFields.roles || showFields.status
          ? [
              {
                title: "权限设置",
                description: "配置用户角色和状态",
                children: (
                  <>
                    {showFields.roles && (
                      <SelectField
                        label="角色"
                        name="roles"
                        required
                        placeholder="请选择角色"
                        options={roleOptions}
                        mode="multiple"
                        rules={[formHelpers.rules.required("请选择角色")]}
                      />
                    )}

                    {showFields.status && (
                      <SelectField
                        label="状态"
                        name="status"
                        required
                        placeholder="请选择状态"
                        options={statusOptions}
                        rules={[formHelpers.rules.required("请选择状态")]}
                      />
                    )}
                  </>
                ),
              },
            ]
          : []),

        ...(showFields.bio || showFields.verification
          ? [
              {
                title: "其他信息",
                description: "用户的其他相关信息",
                children: (
                  <>
                    {showFields.bio && (
                      <TextAreaField
                        label="个人简介"
                        name="bio"
                        placeholder="请输入个人简介"
                        rows={4}
                        maxLength={500}
                        showCount
                      />
                    )}

                    {showFields.verification && (
                      <>
                        <SwitchField
                          label="邮箱已验证"
                          name="emailVerified"
                          checkedChildren="已验证"
                          unCheckedChildren="未验证"
                        />

                        <SwitchField
                          label="手机已验证"
                          name="phoneVerified"
                          checkedChildren="已验证"
                          unCheckedChildren="未验证"
                        />
                      </>
                    )}
                  </>
                ),
              },
            ]
          : []),
      ]}
    />
  );
}
