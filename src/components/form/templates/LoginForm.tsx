"use client";

import React from "react";
import { Form, Checkbox, Divider, Button, Space } from "antd";
import {
  UserOutlined,
  LockOutlined,
  WechatOutlined,
  AlipayOutlined,
} from "@ant-design/icons";
import BaseForm from "../BaseForm";
import { TextField, PasswordField } from "../FormFields";
import { formHelpers } from "../index";
import type { BaseFormProps } from "../BaseForm";

// 登录表单数据接口
export interface LoginFormData {
  username: string;
  password: string;
  remember?: boolean;
  captcha?: string;
}

// 登录表单属性接口
export interface LoginFormProps extends Omit<BaseFormProps, "children"> {
  // 初始数据
  initialData?: Partial<LoginFormData>;

  // 功能配置
  features?: {
    remember?: boolean;
    captcha?: boolean;
    forgotPassword?: boolean;
    register?: boolean;
    socialLogin?: boolean;
  };

  // 第三方登录配置
  socialProviders?: Array<{
    key: string;
    name: string;
    icon: React.ReactNode;
    onClick: () => void;
  }>;

  // 事件处理
  onFinish?: (values: LoginFormData) => Promise<void> | void;
  onForgotPassword?: () => void;
  onRegister?: () => void;
  onCaptchaRefresh?: () => void;

  // 验证码
  captchaUrl?: string;
}

// 默认第三方登录提供商
const defaultSocialProviders = [
  {
    key: "wechat",
    name: "微信",
    icon: <WechatOutlined style={{ color: "#1AAD19" }} />,
    onClick: () => console.log("微信登录"),
  },
  {
    key: "alipay",
    name: "支付宝",
    icon: <AlipayOutlined style={{ color: "#1677FF" }} />,
    onClick: () => console.log("支付宝登录"),
  },
];

export default function LoginForm({
  initialData,
  features = {},
  socialProviders = defaultSocialProviders,
  onFinish,
  onForgotPassword,
  onRegister,
  onCaptchaRefresh,
  captchaUrl,
  form,
  ...baseFormProps
}: LoginFormProps) {
  const [formInstance] = Form.useForm(form);

  // 功能开关
  const showFeatures = {
    remember: features.remember !== false,
    captcha: features.captcha === true,
    forgotPassword: features.forgotPassword !== false,
    register: features.register !== false,
    socialLogin: features.socialLogin !== false && socialProviders.length > 0,
  };

  // 设置初始值
  React.useEffect(() => {
    if (initialData) {
      formHelpers.setInitialValues(formInstance, initialData);
    }
  }, [initialData, formInstance]);

  // 处理表单提交
  const handleFinish = async (values: LoginFormData) => {
    try {
      const formattedValues = formHelpers.formatFormData(values, {
        trimStrings: true,
      });

      await onFinish?.(formattedValues);
    } catch (error) {
      console.error("登录表单提交错误:", error);
      throw error;
    }
  };

  return (
    <BaseForm
      {...baseFormProps}
      form={formInstance}
      onFinish={handleFinish}
      showCancel={false}
      submitText="登录"
      submitButtonProps={{
        size: "large",
        style: { width: "100%" },
      }}
    >
      <TextField
        label=""
        name="username"
        required
        placeholder="用户名/邮箱/手机号"
        prefix={<UserOutlined />}
        size="large"
        rules={[formHelpers.rules.required("请输入用户名")]}
      />

      <PasswordField
        label=""
        name="password"
        required
        placeholder="密码"
        size="large"
        rules={[formHelpers.rules.required("请输入密码")]}
      />

      {showFeatures.captcha && (
        <div style={{ display: "flex", gap: "8px" }}>
          <TextField
            label=""
            name="captcha"
            required
            placeholder="验证码"
            size="large"
            rules={[formHelpers.rules.required("请输入验证码")]}
          />
          <div
            style={{
              width: "120px",
              height: "40px",
              border: "1px solid #d9d9d9",
              borderRadius: "6px",
              cursor: "pointer",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              background: captchaUrl
                ? `url(${captchaUrl}) no-repeat center/cover`
                : "#f5f5f5",
            }}
            onClick={onCaptchaRefresh}
          >
            {!captchaUrl && "点击刷新"}
          </div>
        </div>
      )}

      {/* 记住我和忘记密码 */}
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          margin: "16px 0",
        }}
      >
        {showFeatures.remember && (
          <Form.Item
            name="remember"
            valuePropName="checked"
            style={{ margin: 0 }}
          >
            <Checkbox>记住我</Checkbox>
          </Form.Item>
        )}

        {showFeatures.forgotPassword && (
          <Button type="link" onClick={onForgotPassword} style={{ padding: 0 }}>
            忘记密码？
          </Button>
        )}
      </div>

      {/* 注册链接 */}
      {showFeatures.register && (
        <div style={{ textAlign: "center", marginTop: "16px" }}>
          <span style={{ color: "#666" }}>还没有账号？</span>
          <Button type="link" onClick={onRegister} style={{ padding: "0 4px" }}>
            立即注册
          </Button>
        </div>
      )}

      {/* 第三方登录 */}
      {showFeatures.socialLogin && (
        <>
          <Divider style={{ margin: "24px 0" }}>
            <span style={{ color: "#666", fontSize: "14px" }}>
              其他登录方式
            </span>
          </Divider>

          <div style={{ textAlign: "center" }}>
            <Space size="large">
              {socialProviders.map((provider) => (
                <Button
                  key={provider.key}
                  type="text"
                  size="large"
                  icon={provider.icon}
                  onClick={provider.onClick}
                  style={{
                    width: "40px",
                    height: "40px",
                    borderRadius: "50%",
                    border: "1px solid #d9d9d9",
                  }}
                  title={`${provider.name}登录`}
                />
              ))}
            </Space>
          </div>
        </>
      )}
    </BaseForm>
  );
}
