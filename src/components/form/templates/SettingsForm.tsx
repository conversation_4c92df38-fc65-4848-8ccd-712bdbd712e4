'use client';

import React from 'react';
import { Form } from 'antd';
import BaseForm from '../BaseForm';
import {
  TextField,
  SelectField,
  SwitchField,
  NumberField,
  RadioField,
  SliderField,
} from '../FormFields';
import { formHelpers } from '../index';
import type { BaseFormProps } from '../BaseForm';

// 设置表单数据接口
export interface SettingsFormData {
  // 基本设置
  siteName?: string;
  siteDescription?: string;
  siteKeywords?: string;
  language?: string;
  timezone?: string;
  
  // 功能设置
  enableRegistration?: boolean;
  enableEmailVerification?: boolean;
  enableSmsVerification?: boolean;
  enableTwoFactor?: boolean;
  
  // 安全设置
  passwordMinLength?: number;
  sessionTimeout?: number;
  maxLoginAttempts?: number;
  lockoutDuration?: number;
  
  // 通知设置
  emailNotifications?: boolean;
  smsNotifications?: boolean;
  pushNotifications?: boolean;
  notificationFrequency?: 'immediate' | 'daily' | 'weekly';
  
  // 外观设置
  theme?: 'light' | 'dark' | 'auto';
  primaryColor?: string;
  fontSize?: number;
  compactMode?: boolean;
  
  // 性能设置
  cacheEnabled?: boolean;
  cacheDuration?: number;
  compressionEnabled?: boolean;
  cdnEnabled?: boolean;
}

// 设置表单属性接口
export interface SettingsFormProps extends Omit<BaseFormProps, 'children'> {
  // 设置类型
  type?: 'system' | 'user' | 'security' | 'notification' | 'appearance' | 'performance';
  
  // 初始数据
  initialData?: Partial<SettingsFormData>;
  
  // 提交处理
  onFinish?: (values: SettingsFormData) => Promise<void> | void;
}

// 选项配置
const options = {
  language: [
    { label: '简体中文', value: 'zh-CN' },
    { label: 'English', value: 'en-US' },
    { label: '繁體中文', value: 'zh-TW' },
    { label: '日本語', value: 'ja-JP' },
  ],
  timezone: [
    { label: '北京时间 (UTC+8)', value: 'Asia/Shanghai' },
    { label: '东京时间 (UTC+9)', value: 'Asia/Tokyo' },
    { label: '纽约时间 (UTC-5)', value: 'America/New_York' },
    { label: '伦敦时间 (UTC+0)', value: 'Europe/London' },
  ],
  notificationFrequency: [
    { label: '立即通知', value: 'immediate' },
    { label: '每日汇总', value: 'daily' },
    { label: '每周汇总', value: 'weekly' },
  ],
  theme: [
    { label: '浅色主题', value: 'light' },
    { label: '深色主题', value: 'dark' },
    { label: '跟随系统', value: 'auto' },
  ],
};

export default function SettingsForm({
  type = 'system',
  initialData,
  onFinish,
  form,
  ...baseFormProps
}: SettingsFormProps) {
  const [formInstance] = Form.useForm(form);

  // 设置初始值
  React.useEffect(() => {
    if (initialData) {
      formHelpers.setInitialValues(formInstance, initialData);
    }
  }, [initialData, formInstance]);

  // 处理表单提交
  const handleFinish = async (values: SettingsFormData) => {
    try {
      const formattedValues = formHelpers.formatFormData(values, {
        trimStrings: true,
        removeEmpty: false,
      });

      await onFinish?.(formattedValues);
    } catch (error) {
      console.error('设置表单提交错误:', error);
      throw error;
    }
  };

  // 根据类型渲染不同的设置项
  const renderSettingsByType = () => {
    switch (type) {
      case 'system':
        return [
          {
            title: '基本设置',
            description: '网站的基本信息配置',
            children: (
              <>
                <TextField
                  label="网站名称"
                  name="siteName"
                  placeholder="请输入网站名称"
                  required
                  rules={[formHelpers.rules.required('请输入网站名称')]}
                />
                <TextField
                  label="网站描述"
                  name="siteDescription"
                  placeholder="请输入网站描述"
                />
                <TextField
                  label="关键词"
                  name="siteKeywords"
                  placeholder="请输入关键词，用逗号分隔"
                />
                <SelectField
                  label="默认语言"
                  name="language"
                  placeholder="请选择默认语言"
                  options={options.language}
                />
                <SelectField
                  label="时区"
                  name="timezone"
                  placeholder="请选择时区"
                  options={options.timezone}
                />
              </>
            ),
          },
          {
            title: '功能设置',
            description: '系统功能的开关配置',
            children: (
              <>
                <SwitchField
                  label="允许用户注册"
                  name="enableRegistration"
                  checkedChildren="开启"
                  unCheckedChildren="关闭"
                />
                <SwitchField
                  label="邮箱验证"
                  name="enableEmailVerification"
                  checkedChildren="必须"
                  unCheckedChildren="可选"
                />
                <SwitchField
                  label="短信验证"
                  name="enableSmsVerification"
                  checkedChildren="开启"
                  unCheckedChildren="关闭"
                />
                <SwitchField
                  label="双因子认证"
                  name="enableTwoFactor"
                  checkedChildren="开启"
                  unCheckedChildren="关闭"
                />
              </>
            ),
          },
        ];

      case 'security':
        return [
          {
            title: '密码策略',
            description: '用户密码的安全要求',
            children: (
              <>
                <NumberField
                  label="密码最小长度"
                  name="passwordMinLength"
                  min={6}
                  max={32}
                  placeholder="请输入密码最小长度"
                />
                <NumberField
                  label="会话超时时间"
                  name="sessionTimeout"
                  min={5}
                  max={1440}
                  placeholder="请输入超时时间（分钟）"
                />
              </>
            ),
          },
          {
            title: '登录安全',
            description: '登录失败的处理策略',
            children: (
              <>
                <NumberField
                  label="最大登录尝试次数"
                  name="maxLoginAttempts"
                  min={3}
                  max={10}
                  placeholder="请输入最大尝试次数"
                />
                <NumberField
                  label="锁定持续时间"
                  name="lockoutDuration"
                  min={5}
                  max={60}
                  placeholder="请输入锁定时间（分钟）"
                />
              </>
            ),
          },
        ];

      case 'notification':
        return [
          {
            title: '通知方式',
            description: '选择接收通知的方式',
            children: (
              <>
                <SwitchField
                  label="邮件通知"
                  name="emailNotifications"
                  checkedChildren="开启"
                  unCheckedChildren="关闭"
                />
                <SwitchField
                  label="短信通知"
                  name="smsNotifications"
                  checkedChildren="开启"
                  unCheckedChildren="关闭"
                />
                <SwitchField
                  label="推送通知"
                  name="pushNotifications"
                  checkedChildren="开启"
                  unCheckedChildren="关闭"
                />
                <RadioField
                  label="通知频率"
                  name="notificationFrequency"
                  options={options.notificationFrequency}
                />
              </>
            ),
          },
        ];

      case 'appearance':
        return [
          {
            title: '主题设置',
            description: '自定义界面外观',
            children: (
              <>
                <RadioField
                  label="主题模式"
                  name="theme"
                  options={options.theme}
                />
                <SliderField
                  label="字体大小"
                  name="fontSize"
                  min={12}
                  max={18}
                  marks={{
                    12: '小',
                    14: '中',
                    16: '大',
                    18: '特大',
                  }}
                />
                <SwitchField
                  label="紧凑模式"
                  name="compactMode"
                  checkedChildren="开启"
                  unCheckedChildren="关闭"
                />
              </>
            ),
          },
        ];

      case 'performance':
        return [
          {
            title: '缓存设置',
            description: '提升系统性能的缓存配置',
            children: (
              <>
                <SwitchField
                  label="启用缓存"
                  name="cacheEnabled"
                  checkedChildren="开启"
                  unCheckedChildren="关闭"
                />
                <NumberField
                  label="缓存时长"
                  name="cacheDuration"
                  min={5}
                  max={1440}
                  placeholder="请输入缓存时长（分钟）"
                />
                <SwitchField
                  label="启用压缩"
                  name="compressionEnabled"
                  checkedChildren="开启"
                  unCheckedChildren="关闭"
                />
                <SwitchField
                  label="启用CDN"
                  name="cdnEnabled"
                  checkedChildren="开启"
                  unCheckedChildren="关闭"
                />
              </>
            ),
          },
        ];

      default:
        return [];
    }
  };

  return (
    <BaseForm
      {...baseFormProps}
      form={formInstance}
      onFinish={handleFinish}
      sections={renderSettingsByType()}
    />
  );
}
