"use client";

import React, { useState } from "react";
import { Card, Tabs, message, Button, Space, Divider } from "antd";
import {
  BaseForm,
  TextField,
  PasswordField,
  TextAreaField,
  NumberField,
  SelectField,
  DateField,
  SwitchField,
  RadioField,
  CheckboxField,
  RateField,
  SliderField,
  UserForm,
  LoginForm,
  SettingsForm,
  formHelpers,
} from "@/components/form";

const { TabPane } = Tabs;

export default function FormsExamplePage() {
  const [loading, setLoading] = useState(false);

  // 模拟提交处理
  const handleSubmit = async (values: any, formName: string) => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 1000));
      console.log(`${formName} 提交数据:`, values);
      message.success(`${formName} 提交成功！`);
    } catch (error) {
      message.error("提交失败，请稍后重试");
    } finally {
      setLoading(false);
    }
  };

  // 基础表单字段示例
  const renderBasicFields = () => (
    <BaseForm
      title="基础表单字段"
      description="展示所有可用的表单字段组件"
      loading={loading}
      onFinish={(values) => handleSubmit(values, "基础表单")}
      sections={[
        {
          title: "文本输入",
          children: (
            <>
              <TextField
                label="用户名"
                name="username"
                required
                placeholder="请输入用户名"
                rules={[formHelpers.rules.username]}
              />
              <PasswordField
                label="密码"
                name="password"
                required
                placeholder="请输入密码"
                rules={[formHelpers.rules.password]}
              />
              <TextAreaField
                label="描述"
                name="description"
                placeholder="请输入描述"
                rows={4}
                maxLength={200}
                showCount
              />
            </>
          ),
        },
        {
          title: "数字和选择",
          children: (
            <>
              <NumberField
                label="年龄"
                name="age"
                min={1}
                max={120}
                placeholder="请输入年龄"
              />
              <SelectField
                label="性别"
                name="gender"
                placeholder="请选择性别"
                options={formHelpers.options.gender}
              />
              <DateField
                label="生日"
                name="birthday"
                placeholder="请选择生日"
              />
            </>
          ),
        },
        {
          title: "开关和评分",
          children: (
            <>
              <SwitchField
                label="接收通知"
                name="notifications"
                checkedChildren="开启"
                unCheckedChildren="关闭"
              />
              <RadioField
                label="优先级"
                name="priority"
                options={formHelpers.options.priority}
              />
              <CheckboxField
                label="兴趣爱好"
                name="hobbies"
                options={[
                  { label: "阅读", value: "reading" },
                  { label: "运动", value: "sports" },
                  { label: "音乐", value: "music" },
                  { label: "旅行", value: "travel" },
                ]}
              />
              <RateField
                label="满意度"
                name="satisfaction"
                count={5}
                allowHalf
              />
              <SliderField
                label="价格范围"
                name="priceRange"
                min={0}
                max={1000}
                range
                marks={{
                  0: "¥0",
                  500: "¥500",
                  1000: "¥1000",
                }}
              />
            </>
          ),
        },
      ]}
    />
  );

  // 用户表单示例
  const renderUserForm = () => (
    <UserForm
      title="用户管理表单"
      description="用于创建和编辑用户信息的表单模板"
      mode="create"
      loading={loading}
      onFinish={(values) => handleSubmit(values, "用户表单")}
    />
  );

  // 登录表单示例
  const renderLoginForm = () => (
    <Card title="登录表单" style={{ maxWidth: 400, margin: "0 auto" }}>
      <LoginForm
        loading={loading}
        features={{
          remember: true,
          forgotPassword: true,
          register: true,
          socialLogin: true,
        }}
        onFinish={(values) => handleSubmit(values, "登录表单")}
        onForgotPassword={() => message.info("跳转到忘记密码页面")}
        onRegister={() => message.info("跳转到注册页面")}
      />
    </Card>
  );

  // 设置表单示例
  const renderSettingsForm = () => (
    <Tabs defaultActiveKey="system">
      <TabPane tab="系统设置" key="system">
        <SettingsForm
          type="system"
          loading={loading}
          onFinish={(values) => handleSubmit(values, "系统设置")}
        />
      </TabPane>
      <TabPane tab="安全设置" key="security">
        <SettingsForm
          type="security"
          loading={loading}
          onFinish={(values) => handleSubmit(values, "安全设置")}
        />
      </TabPane>
      <TabPane tab="通知设置" key="notification">
        <SettingsForm
          type="notification"
          loading={loading}
          onFinish={(values) => handleSubmit(values, "通知设置")}
        />
      </TabPane>
      <TabPane tab="外观设置" key="appearance">
        <SettingsForm
          type="appearance"
          loading={loading}
          onFinish={(values) => handleSubmit(values, "外观设置")}
        />
      </TabPane>
      <TabPane tab="性能设置" key="performance">
        <SettingsForm
          type="performance"
          loading={loading}
          onFinish={(values) => handleSubmit(values, "性能设置")}
        />
      </TabPane>
    </Tabs>
  );

  return (
    <div style={{ padding: "0" }}>
      <Card>
        <div style={{ marginBottom: "24px" }}>
          <h2 style={{ margin: 0 }}>表单组件库示例</h2>
          <p style={{ margin: "4px 0 0 0", color: "#666" }}>
            展示可复用的表单组件和模板的使用方法
          </p>
        </div>

        <Tabs defaultActiveKey="basic" size="large">
          <TabPane tab="基础字段" key="basic">
            {renderBasicFields()}
          </TabPane>

          <TabPane tab="用户表单" key="user">
            {renderUserForm()}
          </TabPane>

          <TabPane tab="登录表单" key="login">
            {renderLoginForm()}
          </TabPane>

          <TabPane tab="设置表单" key="settings">
            {renderSettingsForm()}
          </TabPane>
        </Tabs>

        <Divider />

        <div style={{ textAlign: "center" }}>
          <Space>
            <Button
              type="primary"
              onClick={() => message.info("查看表单组件文档")}
            >
              查看文档
            </Button>
            <Button onClick={() => message.info("下载示例代码")}>
              下载代码
            </Button>
          </Space>
        </div>
      </Card>
    </div>
  );
}
