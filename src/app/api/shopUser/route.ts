import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/db";
import bcryptjs from "bcryptjs";
// import zod from "zod";

// GET - 获取商城用户列表
export async function GET(request: NextRequest) {
  try {
    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("pageSize") || "10");
    const keyword = searchParams.get("keyword") || "";
    const status = Number(searchParams.get("status") || "1");

    // 过滤数据
    // let filteredUsers = mockUsers;
    const filteredUsers = await prisma.shopUser.findMany({
      where: {
        nickname: {
          contains: keyword,
        },
        status: status ? Number(status) : undefined,
      },
      skip: (page - 1) * pageSize,
      take: pageSize,
    });

    // 分页
    const total = filteredUsers.length;

    return NextResponse.json({
      success: true,
      code: 200,
      message: "获取商城用户列表成功",
      data: {
        list: filteredUsers,
        pagination: {
          current: page,
          pageSize,
          total,
          totalPages: Math.ceil(total / pageSize),
        },
      },
    });
  } catch (error) {
    console.error("获取商城用户列表错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "服务器内部错误", data: null },
      { status: 500 }
    );
  }
}

// POST - 创建新商城用户
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      nickname,
      email,
      phone,
      status = 1,
      birthday,
      gender,
      avatar,
      password,
      points,
      balance,
    } = body;

    // 验证必填字段
    if (!phone || !nickname || !email) {
      return NextResponse.json(
        {
          success: false,
          code: 400,
          message: "手机号、昵称和邮箱为必填项",
          data: null,
        },
        { status: 400 }
      );
    }

    // 检查商城用户名是否已存在
    // const existingUser = mockUsers.find((u) => u.username === username);
    const existingUser = await prisma.shopUser.findUnique({
      where: {
        phone: phone,
      },
    });
    if (existingUser) {
      return NextResponse.json(
        { success: false, code: 400, message: "商城用户名已存在", data: null },
        { status: 400 }
      );
    }

    // 创建新商城用户
    const newUser = {
      nickname,
      email,
      phone,
      avatar: `https://api.dicebear.com/7.x/avataaars/svg`,
      status: Number(status),
      birthday,
      gender,
      password: bcryptjs.hashSync("123456", 10),
    };
    await prisma.shopUser.create({
      data: newUser,
    });

    // mockUsers.push(newUser);

    return NextResponse.json({
      success: true,
      code: 200,
      message: "创建商城用户成功",
      data: newUser,
    });
  } catch (error) {
    console.error("创建商城用户错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "服务器内部错误", data: null },
      { status: 500 }
    );
  }
}

// OPTIONS - 处理CORS预检请求
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
    },
  });
}
