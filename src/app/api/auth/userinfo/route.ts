import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/db";
import jwt from "jsonwebtoken";

// 模拟用户数据
const mockUsers = [
  {
    id: 1,
    username: "admin",
    nickname: "超级管理员",
    avatar: "https://api.dicebear.com/7.x/miniavs/svg?seed=admin",
    email: "<EMAIL>",
    phone: "13800138000",
    roles: ["admin"],
    permissions: ["*"],
    lastLoginTime: new Date().toISOString(),
  },
  {
    id: 2,
    username: "user",
    nickname: "普通用户",
    avatar: "https://api.dicebear.com/7.x/miniavs/svg?seed=user",
    email: "<EMAIL>",
    phone: "13800138001",
    roles: ["user"],
    permissions: ["read"],
    lastLoginTime: new Date().toISOString(),
  },
];

export async function GET(request: NextRequest) {
  try {
    // 获取Authorization头
    const authorization = request.headers.get("authorization");

    if (!authorization || !authorization.startsWith("Bearer ")) {
      return NextResponse.json(
        {
          code: 401,
          message: "未授权访问",
          success: false,
        },
        { status: 401 }
      );
    }

    const token = authorization.replace("Bearer ", "");

    let userId: number;
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as {
        id: number;
      };
      userId = decoded.id;
    } catch (error) {
      return NextResponse.json(
        {
          code: 401,
          message: "Token无效或已过期",
          success: false,
        },
        { status: 401 }
      );
    }
    // const user = mockUsers.find((u) => u.id === userId);
    const user = await prisma.adminUser.findUnique({
      where: {
        id: userId,
      },
    });

    if (!user) {
      return NextResponse.json(
        {
          code: 404,
          message: "用户不存在",
          success: false,
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      code: 200,
      message: "获取用户信息成功",
      success: true,
      data: { ...user },
    });
  } catch (error) {
    console.error("Get userinfo error:", error);
    return NextResponse.json(
      {
        code: 500,
        message: "服务器内部错误",
        success: false,
      },
      { status: 500 }
    );
  }
}
