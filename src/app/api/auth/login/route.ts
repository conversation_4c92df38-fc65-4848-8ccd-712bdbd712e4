import { prisma } from "@/lib/db";
import { NextRequest, NextResponse } from "next/server";

import bcryptjs from "bcryptjs";
import jwt from "jsonwebtoken";

// // 模拟用户数据
// const mockUsers = [
//   {
//     id: 1,
//     username: "admin",
//     password: "123456",
//     nickname: "超级管理员",
//     avatar: "https://api.dicebear.com/7.x/miniavs/svg?seed=admin",
//     email: "<EMAIL>",
//     phone: "13800138000",
//     roles: ["admin"],
//     permissions: ["*"],
//     lastLoginTime: new Date().toISOString(),
//   },
//   {
//     id: 2,
//     username: "user",
//     password: "123456",
//     nickname: "普通用户",
//     avatar: "https://api.dicebear.com/7.x/miniavs/svg?seed=user",
//     email: "<EMAIL>",
//     phone: "13800138001",
//     roles: ["user"],
//     permissions: ["read"],
//     lastLoginTime: new Date().toISOString(),
//   },
// ];

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { username, password } = body;

    // 查找用户
    // const user = mockUsers.find((u) => u.username === username);

    const user = await prisma.adminUser.findUnique({
      where: {
        username: username,
      },
    });

    if (!user) {
      return NextResponse.json(
        {
          code: 400,
          message: "用户不存在",
          success: false,
        },
        { status: 400 }
      );
    }

    if (!bcryptjs.compareSync(password, user.password)) {
      return NextResponse.json(
        {
          code: 400,
          message: "密码错误",
          success: false,
        },
        { status: 400 }
      );
    }

    // 生成token（实际项目中应该使用JWT）
    // const token = `mock-token-${user.id}-${Date.now()}`;

    const tokenData = {
      id: user.id,
      userName: user.username,
    };
    const token = jwt.sign(tokenData, process.env.JWT_SECRET!, {
      expiresIn: "1d",
    });

    // 返回用户信息（不包含密码）
    const { password: _, ...userInfo } = user;

    return NextResponse.json({
      code: 200,
      message: "登录成功",
      success: true,
      data: {
        token,
        userInfo: {
          ...userInfo,
          roles: ["admin"],
          permissions: ["*"],
        },
      },
    });
  } catch (error) {
    console.error("Login error:", error);
    return NextResponse.json(
      {
        code: 500,
        message: "服务器内部错误",
        success: false,
      },
      { status: 500 }
    );
  }
}
