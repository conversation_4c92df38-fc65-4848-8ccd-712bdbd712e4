import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/db";
import redis from "@/lib/redis";
import { bannnerCacheKey, refreshBannerCache } from "@/lib/bannerCache";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const position = searchParams.get("position");
    const linkType = searchParams.get("linkType");
    const page = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("pageSize") || "10");

    if (position) {
      const cacheKey = bannnerCacheKey(position);
      const cachedData = await redis.get(cacheKey);
      if (cachedData && JSON.parse(cachedData).length) {
        return NextResponse.json({
          success: true,
          code: 200,
          message: "获取banner成功",
          data: {
            list: JSON.parse(cachedData),
            pagination: {
              current: page,
              pageSize,
              total: JSON.parse(cachedData).length,
              totalPages: Math.ceil(JSON.parse(cachedData).length / pageSize),
            },
          },
        });
      }
    }

    const banners = await prisma.banner.findMany({
      where: {
        position: position ? (position as any) : undefined,
        linkType: linkType ? (linkType as any) : undefined,
        status: true,
      },
      orderBy: {
        sort: "asc",
      },
      skip: (page - 1) * pageSize,
      take: pageSize,
    });

    if (position) {
      const cacheKey = bannnerCacheKey(position);
      await redis.set(cacheKey, JSON.stringify(banners), "EX", 60 * 60 * 24); // 24小时过期
    }

    return NextResponse.json({
      success: true,
      code: 200,
      message: "获取banner成功",
      data: {
        list: banners,
        pagination: {
          current: page,
          pageSize,
          total: banners.length,
          totalPages: Math.ceil(banners.length / pageSize),
        },
      },
    });
  } catch (error) {
    console.error("获取banner错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "服务器内部错误", data: null },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      title,
      imageUrl,
      linkType = "none",
      linkValue,
      platform = "all",
      sort = 0,
      status = true,
      startTime,
      endTime,
      position = "home",
    } = body;

    // 验证必填字段
    if (!title || !imageUrl) {
      return NextResponse.json(
        {
          success: false,
          code: 400,
          message: "标题和图片为必填项",
          data: null,
        },
        { status: 400 }
      );
    }

    const baseUrl =
      process.env.NEXT_PUBLIC_API_URL || // 优先使用环境变量
      `${request.nextUrl.protocol}//${request.nextUrl.host}`;
    // 创建新banner
    const newBanner = {
      title,
      imageUrl: imageUrl.startsWith("http")
        ? imageUrl
        : `${baseUrl}${imageUrl}`,
      linkType,
      linkValue,
      platform,
      sort,
      status,
      startTime,
      endTime,
      position,
    };
    await prisma.banner.create({
      data: newBanner,
    });
    const cacheKey = bannnerCacheKey(position);
    await refreshBannerCache(cacheKey, position);

    return NextResponse.json({
      success: true,
      code: 200,
      message: "创建banner成功",
      data: newBanner,
    });
  } catch (error) {
    console.error("创建banner错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "服务器内部错误", data: null },
      { status: 500 }
    );
  }
}
