import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/db";
import dayjs from "dayjs";

// GET - 获取单个订单信息
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // 订单详情 关联的商店 和station
    const targetOrder = await prisma.order.findUnique({
      where: {
        id,
      },
      include: {
        rechargeOrder: {
          include: {
            shopUser: true,
          },
        },
        consumeOrder: {
          include: {
            car: true,
            station: true,
          },
        },
      },
    });

    if (!targetOrder) {
      return NextResponse.json(
        { success: false, code: 404, message: "订单不存在", data: null },
        { status: 404 }
      );
    }
    const durationMinutes = dayjs(targetOrder.completedAt).diff(
      targetOrder.createdAt,
      "minute"
    );

    return NextResponse.json({
      success: true,
      code: 200,
      message: "获取订单信息成功",
      data: {
        ...targetOrder,
        durationMinutes,
      },
    });
  } catch (error) {
    console.error("获取订单信息错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "服务器内部错误", data: null },
      { status: 500 }
    );
  }
}

// PUT - 更新订单信息
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const body = await request.json();
    const { status, consume, recharge, type } = body;
    const { id } = await params;

    const targetOrder = await prisma.order.findUnique({
      where: {
        id,
      },
      include: { consumeOrder: true, rechargeOrder: true },
    });

    if (!targetOrder) {
      return NextResponse.json(
        { success: false, code: 404, message: "订单不存在", data: null },
        { status: 404 }
      );
    }

    // 更新订单信息
    const updatedOrder = {
      ...targetOrder,
      ...(status && { status }),
      ...(consume && { consume }),
      ...(recharge && { recharge }),
      ...(type && { type }),
      updatedAt: dayjs().format("YYYY-MM-DD HH:mm:ss"),
    };

    if (type === "CONSUME" && type === "WASH") {
      updatedOrder.consumeOrder = {
        ...targetOrder.consumeOrder,
        ...(consume && { consume }),
      };
    } else {
      updatedOrder.rechargeOrder = {
        ...targetOrder.rechargeOrder,
        ...(recharge && { recharge }),
      };
    }
    await prisma.order.update({
      where: {
        id,
      },
      data: updatedOrder,
    });

    return NextResponse.json({
      success: true,
      code: 200,
      message: "更新订单信息成功",
      data: updatedOrder,
    });
  } catch (error) {
    console.error("更新订单信息错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "服务器内部错误", data: null },
      { status: 500 }
    );
  }
}

// DELETE - 删除订单
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const targetOrder = await prisma.order.findUnique({
      where: {
        id,
      },
    });

    if (!targetOrder) {
      return NextResponse.json(
        { success: false, code: 404, message: "订单不存在", data: null },
        { status: 404 }
      );
    }

    await prisma.order.delete({
      where: {
        id,
      },
    });

    return NextResponse.json({
      success: true,
      code: 200,
      message: "删除订单成功",
      data: null,
    });
  } catch (error) {
    console.error("删除订单错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "服务器内部错误", data: null },
      { status: 500 }
    );
  }
}
