import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/db";
import redis from "@/lib/redis";
import { generateOrderNo } from "@/utils/generateOrderNo";
import { z } from "zod";
import { cleanObject } from "@/utils/cleanObject";
import { validateRequest } from "@/lib/validate";

const createOrderSchema = z.object({
  shopUserId: z.coerce.number().min(1, "商户用户ID不能为空"),
  stationId: z.coerce.number().min(1, "工位ID不能为空"),
  carId: z.number().min(1, "车辆ID不能为空"),
  packageId: z.string().optional(),
  type: z.enum(["WASH", "RECHARGE"]).default("WASH"),
});

export type CreateOrderBody = z.infer<typeof createOrderSchema>;

export async function POST(req: NextRequest) {
  try {
    const validationResult = await validateRequest(req, createOrderSchema);

    const { shopUserId, stationId, carId, packageId, type } = validationResult;
    const lockKey = `order:create:${shopUserId}`;
    const lock = await redis.set(lockKey, "1", "EX", 2);
    if (!lock) {
      return NextResponse.json({ code: 400, msg: "请勿重复提交" });
    }

    const orderNoMap: any = {
      RECHARGE: "CZ", // 充值
      WASH: "XF", // 洗车
    };
    const no = orderNoMap[type];
    // 生成订单号
    const orderNo = await generateOrderNo(no);

    // Get station to retrieve shopId
    const station = await prisma.station.findUnique({
      where: { id: Number(stationId) },

      include: {
        shop: true,
      },
    });

    if (!station) {
      return NextResponse.json({ code: 400, msg: "工位不存在" });
    }

    const order = await prisma.order.create({
      data: {
        orderNo,
        shopUserId,
        shopId: station.shopId,
        type,
        amount: 0,
        status: "PENDING",
        consumeOrder: {
          create: {
            packageId,
            carId: Number(carId),
            stationId,
          },
        },
      },
      include: {
        consumeOrder: {
          include: {
            car: true,
            station: true,
          },
        },
      },
    });

    await redis.del(lockKey);

    // 更新洗车站为使用中
    await prisma.station.update({
      where: {
        id: stationId,
      },
      data: {
        status: "IN_USE",
      },
    });
    return NextResponse.json({
      code: 200,
      msg: "消费订单创建成功",
      data: order,
    });
  } catch (error: any) {
    console.error("创建消费订单失败:", error);
    return NextResponse.json({ code: 500, msg: "创建消费订单失败" });
  }
}

const getOrderSchema = z.object({
  type: z.enum(["WASH", "RECHARGE"]).optional(),
  shopUserId: z.number().min(1, "商户用户ID不能为空"),
  shopId: z.number().optional(),
  status: z.enum(["PENDING", "PAID", "FAILED", "REFUNDED"]).optional(),
  page: z.string().transform(Number).default(1),
  pageSize: z.string().transform(Number).default(10),
  keyword: z.string().optional(),
  carId: z.string().optional(),
  stationId: z.string().optional(),
});

export async function GET(req: NextRequest) {
  try {
    const validationResult = await validateRequest(req, getOrderSchema);

    const {
      type,
      shopUserId,
      shopId,
      status,
      keyword = "",
      page,
      pageSize,
      carId,
      stationId,
    } = validationResult;
    // 过滤数据
    // 关联查询
    const where = cleanObject({
      type: type ? (type as any) : undefined,
      orderNo: {
        contains: keyword,
      },
      status: status ? (status as any) : undefined,
      shopId: shopId ? Number(shopId) : undefined,
      shopUserId: shopUserId ? Number(shopUserId) : undefined,
      consumeOrder: {
        carId: carId ? Number(carId) : undefined,
        stationId: stationId ? Number(stationId) : undefined,
      },
    });
    const filteredOrders = await prisma.order.findMany({
      where,
      include: {
        consumeOrder: {
          include: {
            car: true,
            station: true,
          },
        },
      },
      skip: (page - 1) * pageSize,
      take: pageSize,
    });

    return NextResponse.json({
      success: true,
      code: 200,
      message: "获取订单列表成功",
      data: {
        list: filteredOrders,
        pagination: {
          current: page,
          pageSize,
          total: filteredOrders.length,
          totalPages: Math.ceil(filteredOrders.length / pageSize),
        },
      },
    });
  } catch (error) {
    console.error("获取订单列表错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "服务器内部错误", data: null },
      { status: 500 }
    );
  }
}
