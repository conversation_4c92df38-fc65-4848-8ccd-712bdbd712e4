import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/db";

// GET - 获取单个订单信息
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    const targetOrder = await prisma.order.findUnique({
      where: {
        id,
      },
      include: {
        consumeOrder: {
          include: {
            car: true,
            station: true,
          },
        },
      },
    });

    if (!targetOrder) {
      return NextResponse.json(
        { success: false, code: 404, message: "订单不存在", data: null },
        { status: 404 }
      );
    }
    return NextResponse.json({
      success: true,
      code: 200,
      message: "获取订单信息成功",
      data: targetOrder,
    });
  } catch (error) {
    console.error("获取订单信息错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "服务器内部错误", data: null },
      { status: 500 }
    );
  }
}

// PUT - 更新订单信息
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const body = await request.json();
    const { status, consume } = body;
    const { id } = await params;
    const targetOrder = await prisma.order.findUnique({
      where: {
        id,
      },
    });

    if (!targetOrder) {
      return NextResponse.json(
        { success: false, code: 404, message: "订单不存在", data: null },
        { status: 404 }
      );
    }

    // 更新订单信息
    const updatedOrder = {
      ...targetOrder,
      ...(status && { status }),
      ...(consume && { consume }),
      updatedAt: new Date().toISOString(),
    };

    await prisma.order.update({
      where: {
        id,
      },
      data: updatedOrder,
    });

    return NextResponse.json({
      success: true,
      code: 200,
      message: "更新订单信息成功",
      data: updatedOrder,
    });
  } catch (error) {
    console.error("更新订单信息错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "服务器内部错误", data: null },
      { status: 500 }
    );
  }
}

// DELETE - 删除订单
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const targetOrder = await prisma.order.findUnique({
      where: {
        id,
      },
    });

    if (!targetOrder) {
      return NextResponse.json(
        { success: false, code: 404, message: "订单不存在", data: null },
        { status: 404 }
      );
    }

    await prisma.order.delete({
      where: {
        id,
      },
    });

    return NextResponse.json({
      success: true,
      code: 200,
      message: "删除订单成功",
      data: null,
    });
  } catch (error) {
    console.error("删除订单错误:", error);
    return NextResponse.json(
      { success: false, code: 500, message: "服务器内部错误", data: null },
      { status: 500 }
    );
  }
}
