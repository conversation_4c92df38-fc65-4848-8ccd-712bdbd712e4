import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/db";

import bcryptjs from "bcryptjs";
import jwt from "jsonwebtoken";

export async function POST(req: NextRequest) {
  const { code, phone } = await req.json();

  //   const appid = process.env.WX_APPID!;
  //   const secret = process.env.WX_SECRET!;
  //   const url = `https://api.weixin.qq.com/sns/jscode2session?appid=${appid}&secret=${secret}&js_code=${code}&grant_type=authorization_code`;

  //   const res = await fetch(url);
  //   const data = await res.json(); // 包含 openid、session_key

  if (code !== 123456) {
    return NextResponse.json({
      success: false,
      code: 400,
      message: "code错误",
      data: null,
    });
  }

  let user = await prisma.shopUser.findFirst({
    where: {
      phone,
    },
  });

  // 你可以生成自己的 JWT 作为登录凭证
  // 也可以在 DB 保存用户

  if (!user) {
    user = await prisma.shopUser.create({
      data: {
        nickname: "user",
        email: "",
        phone,
        avatar: ``,
        status: 1,
        birthday: new Date(),
        gender: 1,
        password: bcryptjs.hashSync("123456", 10),
        lastLoginAt: new Date(),
        points: 0,
        balance: 0,
      
      },
    });
  }

  const tokenData = {
    id: user.id,
    userName: user.nickname,
  };
  const token = jwt.sign(tokenData, process.env.JWT_SECRET!, {
    expiresIn: "1d",
  });

  // 返回用户信息（不包含密码）
  const { password: _, ...userInfo } = user;

  return NextResponse.json({
    code: 200,
    message: "登录成功",
    success: true,
    data: {
      token,
      userInfo,
    },
  });
}
